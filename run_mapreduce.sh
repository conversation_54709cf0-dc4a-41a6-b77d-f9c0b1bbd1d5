#!/bin/bash

# 这个脚本用于解决MapReduce作业的依赖问题并运行作业
# 作者：Augment Agent
# 日期：2025-05-19

# 显示脚本使用方法
show_usage() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -h, --help       显示帮助信息"
    echo "  -c, --clean      清理临时文件和输出目录"
    echo "  -y, --year YEAR  指定筛选的年份 (默认: 2020)"
    echo "  -m, --method NUM 指定使用的方法 (1-6, 默认: 1)"
    echo "                   1: 设置HADOOP_CLASSPATH"
    echo "                   2: 使用-libjars选项（已增强）"
    echo "                   3: 使用-files选项"
    echo "                   4: 创建胖JAR文件（已增强）"
    echo "                   5: 直接使用hadoop命令"
    echo "                   6: 使用SimpleBookFilter类（不依赖MapReduce框架）"
    echo "  -i, --input PATH 指定输入路径 (默认: /user/hadoop/input/books.csv)"
    echo "  -o, --output PATH 指定输出路径 (默认: /user/hadoop/output)"
}

# 默认参数
YEAR=2020
METHOD=1
INPUT_PATH="/user/hadoop/input/books.csv"
OUTPUT_PATH="/user/hadoop/output"
CLEAN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -h|--help)
            show_usage
            exit 0
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -y|--year)
            YEAR="$2"
            shift
            shift
            ;;
        -m|--method)
            METHOD="$2"
            shift
            shift
            ;;
        -i|--input)
            INPUT_PATH="$2"
            shift
            shift
            ;;
        -o|--output)
            OUTPUT_PATH="$2"
            shift
            shift
            ;;
        *)
            echo "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
done

# 显示当前配置
echo "当前配置:"
echo "  筛选年份: $YEAR"
echo "  使用方法: $METHOD"
echo "  输入路径: $INPUT_PATH"
echo "  输出路径: $OUTPUT_PATH"
echo "  清理模式: $CLEAN"
echo ""

# 确保我们在项目目录中
if [ ! -f "BookFilter.jar" ]; then
    echo "错误: 找不到BookFilter.jar文件"
    echo "请确保您在项目目录中运行此脚本"
    exit 1
fi

# 如果需要清理，删除临时文件和输出目录
if [ "$CLEAN" = true ]; then
    echo "清理临时文件和输出目录..."
    rm -rf fatjar_temp BookFilterFat.jar
    hadoop fs -rm -r $OUTPUT_PATH
    echo "清理完成"
    echo ""
fi

# 删除已存在的输出目录
echo "删除已存在的输出目录..."
hadoop fs -rm -r $OUTPUT_PATH
echo ""

# 根据选择的方法运行MapReduce作业
case $METHOD in
    1)
        echo "方法1: 设置HADOOP_CLASSPATH环境变量"
        echo "设置HADOOP_CLASSPATH..."
        export HADOOP_CLASSPATH=$(hadoop classpath)
        echo "运行MapReduce作业..."
        hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver $INPUT_PATH $OUTPUT_PATH $YEAR
        ;;
    2)
        echo "方法2: 使用-libjars选项"
        echo "运行MapReduce作业..."
        # 添加更多依赖JAR包
        LIBJARS="$HADOOP_HOME/share/hadoop/mapreduce/hadoop-mapreduce-client-core-*.jar"
        LIBJARS="$LIBJARS,$HADOOP_HOME/share/hadoop/mapreduce/hadoop-mapreduce-client-common-*.jar"
        LIBJARS="$LIBJARS,$HADOOP_HOME/share/hadoop/common/hadoop-common-*.jar"
        LIBJARS="$LIBJARS,$HADOOP_HOME/share/hadoop/common/lib/hadoop-annotations-*.jar"
        LIBJARS="$LIBJARS,$HADOOP_HOME/share/hadoop/mapreduce/hadoop-mapreduce-client-app-*.jar"
        LIBJARS="$LIBJARS,$HADOOP_HOME/share/hadoop/mapreduce/hadoop-mapreduce-client-jobclient-*.jar"
        LIBJARS="$LIBJARS,$HADOOP_HOME/share/hadoop/yarn/hadoop-yarn-client-*.jar"
        LIBJARS="$LIBJARS,$HADOOP_HOME/share/hadoop/yarn/hadoop-yarn-common-*.jar"
        LIBJARS="$LIBJARS,$HADOOP_HOME/share/hadoop/yarn/hadoop-yarn-api-*.jar"

        hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver -libjars $LIBJARS $INPUT_PATH $OUTPUT_PATH $YEAR
        ;;
    3)
        echo "方法3: 使用-files选项"
        echo "运行MapReduce作业..."
        hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver -files BookFilter.jar $INPUT_PATH $OUTPUT_PATH $YEAR
        ;;
    4)
        echo "方法4: 创建包含所有依赖的胖JAR文件"
        echo "创建临时目录..."
        mkdir -p fatjar_temp
        echo "解压JAR文件..."
        cd fatjar_temp
        jar xf ../BookFilter.jar
        echo "复制Hadoop依赖..."
        # 复制所有Hadoop相关依赖
        cp -r $HADOOP_HOME/share/hadoop/common/lib/* .
        cp -r $HADOOP_HOME/share/hadoop/mapreduce/lib/* .
        cp -r $HADOOP_HOME/share/hadoop/common/*.jar .
        cp -r $HADOOP_HOME/share/hadoop/mapreduce/*.jar .
        cp -r $HADOOP_HOME/share/hadoop/yarn/*.jar .
        cp -r $HADOOP_HOME/share/hadoop/hdfs/*.jar .
        cp -r $HADOOP_HOME/share/hadoop/client/*.jar .

        # 确保包含MRAppMaster类
        echo "确保包含MRAppMaster类..."
        find $HADOOP_HOME -name "*.jar" -exec grep -l "org/apache/hadoop/mapreduce/v2/app/MRAppMaster.class" {} \; | xargs -I {} cp {} .

        echo "创建胖JAR文件..."
        jar cvf ../BookFilterFat.jar *
        cd ..
        echo "运行MapReduce作业..."
        hadoop jar BookFilterFat.jar com.hadoop.bookfilter.BookFilterDriver $INPUT_PATH $OUTPUT_PATH $YEAR
        ;;
    5)
        echo "方法5: 直接使用hadoop命令"
        echo "运行MapReduce作业..."
        hadoop com.hadoop.bookfilter.BookFilterDriver $INPUT_PATH $OUTPUT_PATH $YEAR
        ;;
    6)
        echo "方法6: 使用SimpleBookFilter类（不依赖MapReduce框架）"
        echo "运行SimpleBookFilter..."
        # 设置CLASSPATH
        export CLASSPATH=BookFilter.jar:$(hadoop classpath)
        # 运行SimpleBookFilter类
        java com.hadoop.bookfilter.SimpleBookFilter $INPUT_PATH $OUTPUT_PATH $YEAR
        ;;
    *)
        echo "错误: 无效的方法编号: $METHOD"
        show_usage
        exit 1
        ;;
esac

# 检查作业是否成功
if [ $? -eq 0 ]; then
    echo ""
    echo "MapReduce作业成功完成!"
    echo "查看结果:"
    echo "hadoop fs -ls $OUTPUT_PATH"
    echo "hadoop fs -cat $OUTPUT_PATH/part-r-*"
else
    echo ""
    echo "MapReduce作业失败!"
    echo "请尝试其他方法或检查日志获取更多信息"
fi
