package com.hadoop.bookfilter;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.FSDataInputStream;
import org.apache.hadoop.fs.FSDataOutputStream;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 简化版图书筛选实现
 * 不依赖Hadoop MapReduce框架
 */
public class SimpleBookFilter {

    public static void main(String[] args) throws Exception {
        System.out.println("开始使用简化版本处理图书筛选...");

        // 设置目标年份和路径
        int targetYear = 2020;
        String inputPath = "src/main/resources/books.csv";
        String outputPath = "output";

        // 解析命令行参数
        if (args.length >= 3) {
            // 如果有足够的参数，按照MapReduce格式解析
            inputPath = args[0];
            outputPath = args[1];
            try {
                targetYear = Integer.parseInt(args[2]);
            } catch (NumberFormatException e) {
                System.err.println("无效的年份参数，使用默认值2020");
            }
        } else if (args.length >= 1) {
            // 如果只有一个参数，假设是年份
            try {
                targetYear = Integer.parseInt(args[0]);
            } catch (NumberFormatException e) {
                System.err.println("无效的年份参数，使用默认值2020");
            }
        }

        System.out.println("目标年份: " + targetYear);
        System.out.println("输入路径: " + inputPath);
        System.out.println("输出路径: " + outputPath);

        // 检查是否是HDFS路径
        boolean isHdfsPath = inputPath.startsWith("hdfs://") || inputPath.startsWith("/user/");

        List<String> filteredBooks = new ArrayList<>();

        if (isHdfsPath) {
            // 处理HDFS路径
            processHdfsFile(inputPath, outputPath, targetYear, filteredBooks);
        } else {
            // 处理本地文件
            processLocalFile(inputPath, outputPath, targetYear, filteredBooks);
        }

        // 打印结果
        System.out.println("筛选完成，共找到 " + filteredBooks.size() + " 本 " + targetYear + " 年出版的图书");

        // 显示筛选结果
        System.out.println("\n筛选结果：");
        System.out.println("图书ID\t书名\t作者\t出版年份\t出版社\t价格");
        for (String book : filteredBooks) {
            String[] fields = book.split(",");
            if (fields.length >= 6) {
                System.out.println(fields[0] + "\t" + fields[1] + "\t" + fields[2] + "\t" +
                        fields[3] + "\t" + fields[4] + "\t" + fields[5]);
            }
        }

        System.out.println("\n处理完成");
    }

    /**
     * 处理HDFS文件
     */
    private static void processHdfsFile(String inputPath, String outputPath, int targetYear, List<String> filteredBooks) throws Exception {
        // 创建Hadoop配置
        Configuration conf = new Configuration();
        FileSystem fs = FileSystem.get(conf);

        // 读取输入文件
        Path inPath = new Path(inputPath);
        if (!fs.exists(inPath)) {
            System.err.println("输入文件不存在: " + inputPath);
            return;
        }

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(fs.open(inPath), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                String[] fields = line.split(",");

                // 确保数据格式正确
                if (fields.length >= 4) {
                    try {
                        // 获取出版年份
                        int publishYear = Integer.parseInt(fields[3]);

                        // 如果年份匹配，则添加到结果列表
                        if (publishYear == targetYear) {
                            filteredBooks.add(line);
                        }
                    } catch (NumberFormatException e) {
                        // 忽略无法解析年份的记录
                        System.err.println("无法解析年份: " + line);
                    }
                }
            }
        }

        // 写入输出文件
        Path outPath = new Path(outputPath);
        Path outFile = new Path(outputPath + "/part-r-00000");

        // 如果输出目录已存在，先删除
        if (fs.exists(outPath)) {
            fs.delete(outPath, true);
        }

        // 创建输出目录
        fs.mkdirs(outPath);

        // 写入结果
        try (FSDataOutputStream out = fs.create(outFile);
             OutputStreamWriter writer = new OutputStreamWriter(out, StandardCharsets.UTF_8)) {
            for (String book : filteredBooks) {
                String[] fields = book.split(",");
                writer.write(fields[0] + "\t" + book + "\n");
            }
        }

        System.out.println("结果已保存到HDFS: " + outFile.toString());
    }

    /**
     * 处理本地文件
     */
    private static void processLocalFile(String inputPath, String outputPath, int targetYear, List<String> filteredBooks) throws IOException {
        // 创建输出目录
        File outputDir = new File(outputPath);
        if (outputDir.exists()) {
            deleteDir(outputDir);
        }
        outputDir.mkdirs();

        // 读取输入文件
        try (BufferedReader reader = new BufferedReader(new FileReader(inputPath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                String[] fields = line.split(",");

                // 确保数据格式正确
                if (fields.length >= 4) {
                    try {
                        // 获取出版年份
                        int publishYear = Integer.parseInt(fields[3]);

                        // 如果年份匹配，则添加到结果列表
                        if (publishYear == targetYear) {
                            filteredBooks.add(line);
                        }
                    } catch (NumberFormatException e) {
                        // 忽略无法解析年份的记录
                        System.err.println("无法解析年份: " + line);
                    }
                }
            }
        }

        // 写入输出文件
        File outputFile = new File(outputPath, "part-r-00000");
        try (OutputStreamWriter writer = new OutputStreamWriter(
                new FileOutputStream(outputFile), StandardCharsets.UTF_8)) {
            for (String book : filteredBooks) {
                String[] fields = book.split(",");
                writer.write(fields[0] + "\t" + book + "\n");
            }
        }

        System.out.println("结果已保存到本地: " + outputFile.getAbsolutePath());
    }

    /**
     * 递归删除目录
     */
    private static boolean deleteDir(File dir) {
        if (dir.isDirectory()) {
            String[] children = dir.list();
            if (children != null) {
                for (String child : children) {
                    boolean success = deleteDir(new File(dir, child));
                    if (!success) {
                        return false;
                    }
                }
            }
        }
        return dir.delete();
    }
}
