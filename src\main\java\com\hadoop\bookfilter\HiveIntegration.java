package com.hadoop.bookfilter;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Hive集成类
 * 用于与Hive数据仓库交互
 */
public class HiveIntegration {

    // JDBC连接URL
    private static final String JDBC_URL = "************************************";
    
    // JDBC驱动类
    private static final String JDBC_DRIVER = "org.apache.hive.jdbc.HiveDriver";
    
    /**
     * 创建图书表
     */
    public static void createBooksTable() {
        try (Connection conn = getConnection();
             Statement stmt = conn.createStatement()) {
            
            // 如果表已存在，则删除
            stmt.execute("DROP TABLE IF EXISTS books");
            
            // 创建图书表
            String createTableSQL = "CREATE TABLE books (" +
                    "id STRING, " +
                    "title STRING, " +
                    "author STRING, " +
                    "publish_year INT, " +
                    "publisher STRING, " +
                    "price DOUBLE) " +
                    "ROW FORMAT DELIMITED " +
                    "FIELDS TERMINATED BY ',' " +
                    "STORED AS TEXTFILE";
            
            stmt.execute(createTableSQL);
            System.out.println("图书表创建成功");
            
        } catch (SQLException | ClassNotFoundException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 加载数据到图书表
     * @param dataPath 数据文件路径
     */
    public static void loadData(String dataPath) {
        try (Connection conn = getConnection();
             Statement stmt = conn.createStatement()) {
            
            // 加载数据
            String loadDataSQL = "LOAD DATA INPATH '" + dataPath + "' OVERWRITE INTO TABLE books";
            stmt.execute(loadDataSQL);
            System.out.println("数据加载成功");
            
        } catch (SQLException | ClassNotFoundException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 查询指定年份的图书
     * @param year 目标年份
     */
    public static void queryBooksByYear(int year) {
        try (Connection conn = getConnection();
             Statement stmt = conn.createStatement()) {
            
            // 查询指定年份的图书
            String querySQL = "SELECT * FROM books WHERE publish_year = " + year;
            ResultSet rs = stmt.executeQuery(querySQL);
            
            // 打印结果
            System.out.println("\n查询结果：");
            System.out.println("图书ID\t书名\t作者\t出版年份\t出版社\t价格");
            
            while (rs.next()) {
                System.out.println(rs.getString("id") + "\t" +
                        rs.getString("title") + "\t" +
                        rs.getString("author") + "\t" +
                        rs.getInt("publish_year") + "\t" +
                        rs.getString("publisher") + "\t" +
                        rs.getDouble("price"));
            }
            
        } catch (SQLException | ClassNotFoundException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 获取Hive连接
     * @return Hive连接
     */
    private static Connection getConnection() throws SQLException, ClassNotFoundException {
        // 加载JDBC驱动
        Class.forName(JDBC_DRIVER);
        
        // 创建连接
        return DriverManager.getConnection(JDBC_URL);
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        if (args.length < 2) {
            System.err.println("用法: HiveIntegration <数据文件路径> <目标年份>");
            System.exit(1);
        }
        
        String dataPath = args[0];
        int targetYear = Integer.parseInt(args[1]);
        
        // 创建表
        createBooksTable();
        
        // 加载数据
        loadData(dataPath);
        
        // 查询数据
        queryBooksByYear(targetYear);
    }
}
