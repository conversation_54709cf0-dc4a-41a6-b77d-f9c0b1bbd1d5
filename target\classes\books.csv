1,Java编程思想,<PERSON>,2007,机械工业出版社,108.00
2,<PERSON><PERSON>权威指南,<PERSON>,2015,清华大学出版社,119.00
3,Spark快速大数据分析,<PERSON>,2015,人民邮电出版社,69.00
4,深入理解Java虚拟机,周志明,2019,机械工业出版社,129.00
5,Effective Java,Joshua Bloch,2018,机械工业出版社,99.00
6,Spring实战,Craig Walls,2020,人民邮电出版社,89.00
7,<PERSON>编<PERSON>,<PERSON>,2013,机械工业出版社,89.00
8,算法导论,<PERSON>,2009,机械工业出版社,128.00
9,深度学习,<PERSON>,2017,人民邮电出版社,168.00
10,数据结构与算法分析,<PERSON>,2020,机械工业出版社,78.00
11,计算机网络,<PERSON>,2011,机械工业出版社,89.00
12,操作系统概念,<PERSON>,2014,高等教育出版社,98.00
13,编译原理,<PERSON>,2008,机械工业出版社,85.00
14,数据库系统概念,<PERSON>,2012,机械工业出版社,99.00
15,人工智能:一种现代方法,<PERSON>,2020,电子工业出版社,119.00
16,机器学习实战,<PERSON> <PERSON>,2013,人民邮电出版社,69.00
17,大数据技术与应用,林子雨,2020,人民邮电出版社,79.00
18,云计算架构技术与实践,刘鹏,2016,电子工业出版社,89.00
19,软件工程:实践者的研究方法,Roger S. Pressman,2010,机械工业出版社,109.00
20,计算机组成与设计,David A. Patterson,2015,机械工业出版社,99.00
21,C++ Primer,Stanley B. Lippman,2013,电子工业出版社,128.00
22,JavaScript高级程序设计,Nicholas C. Zakas,2020,人民邮电出版社,129.00
23,代码整洁之道,Robert C. Martin,2010,人民邮电出版社,79.00
24,重构:改善既有代码的设计,Martin Fowler,2019,人民邮电出版社,118.00
25,设计模式:可复用面向对象软件的基础,Erich Gamma,1994,机械工业出版社,99.00
26,计算机程序的构造和解释,Harold Abelson,2008,机械工业出版社,88.00
27,Go语言程序设计,Alan A. A. Donovan,2016,机械工业出版社,99.00
28,流畅的Python,Luciano Ramalho,2017,人民邮电出版社,138.00
29,鸟哥的Linux私房菜基础学习篇,鸟哥,2018,人民邮电出版社,158.00
30,TCP/IP详解 卷1:协议,W. Richard Stevens,2009,机械工业出版社,99.00
31,Web应用安全权威指南,Paco Hope,2015,机械工业出版社,79.00
32,人月神话,Frederick P. Brooks Jr.,2015,清华大学出版社,68.00
33,程序员修炼之道:从小工到专家,Andrew Hunt,2011,人民邮电出版社,79.00
34,UNIX环境高级编程,W. Richard Stevens,2014,人民邮电出版社,149.00
35,深入理解计算机系统,Randal E. Bryant,2017,机械工业出版社,159.00
36,机器学习,周志华,2016,清华大学出版社,88.00
37,统计学习方法,李航,2019,清华大学出版社,75.00
38,模式识别与机器学习,Christopher M. Bishop,2009,电子工业出版社,99.00
39,自然语言处理综论,Daniel Jurafsky,2008,电子工业出版社,118.00
40,计算机图形学,Donald Hearn,2014,电子工业出版社,108.00
41,数据密集型应用系统设计,Martin Kleppmann,2018,中国电力出版社,99.00
42,区块链技术指南,Arvind Narayanan,2017,机械工业出版社,79.00
43,Kubernetes权威指南,Kelsey Hightower,2020,电子工业出版社,119.00
44,DevOps实践指南,Gene Kim,2018,人民邮电出版社,99.00
45,软件测试的艺术,Glenford J. Myers,2012,机械工业出版社,69.00
46,编程珠玑,Jon Bentley,2008,人民邮电出版社,60.00
47,密码编码学与网络安全:原理与实践,William Stallings,2019,电子工业出版社,109.00
48,敏捷软件开发:原则、模式与实践,Robert C. Martin,2013,清华大学出版社,85.00
49,领域驱动设计:软件核心复杂性应对之道,Eric Evans,2016,人民邮电出版社,108.00
50,图解HTTP,上野宣,2014,人民邮电出版社,59.00
51,Hive编程指南,Edward Capriolo,2016,机械工业出版社,89.00
52,Flink实战,崔星灿,2021,电子工业出版社,99.00
53,HBase权威指南,Lars George,2017,清华大学出版社,109.00
54,Kafka权威指南,Neha Narkhede,2018,人民邮电出版社,79.00
55,ZooKeeper:分布式过程协同技术详解,Flavio Junqueira,2014,机械工业出版社,69.00
56,Presto技术内幕,Baron Schwartz,2019,电子工业出版社,89.00
57,Scala编程,Martin Odersky,2018,人民邮电出版社,119.00
58,Storm分布式实时计算模式,P. Taylor Goetz,2015,人民邮电出版社,79.00
59,Elasticsearch实战,Radu Gheorghe,2017,人民邮电出版社,99.00
60,Redis设计与实现,黄健宏,2014,机械工业出版社,79.00
61,MongoDB权威指南,Kristina Chodorow,2016,人民邮电出版社,89.00
62,Neo4j实战,Aleksa Vukotic,2018,人民邮电出版社,99.00
63,Cassandra权威指南,Jeff Carpenter,2017,机械工业出版社,89.00
64,Druid实时大数据分析原理与实践,范欣欣,2019,电子工业出版社,99.00
65,ClickHouse原理解析与应用实践,朱凯,2020,机械工业出版社,109.00
66,Impala实战,John Russell,2017,人民邮电出版社,89.00
67,Kylin权威指南,韩卿,2018,电子工业出版社,99.00
68,Sqoop实战指南,Kathleen Ting,2015,人民邮电出版社,69.00
69,Flume构建高可用、可扩展的海量日志采集系统,Alan Gates,2016,机械工业出版社,79.00
70,Oozie权威指南,Mohammad Islam,2015,清华大学出版社,89.00
71,Pig编程指南,Alan Gates,2014,人民邮电出版社,79.00
72,Mahout实战,Sean Owen,2016,人民邮电出版社,99.00
73,Ambari企业级大数据平台构建与管理,Jeff Markham,2018,电子工业出版社,109.00
74,Yarn资源管理与调度,Arun C. Murthy,2015,机械工业出版社,89.00
75,Zeppelin大数据可视化实战,Shiro Matsuo,2019,人民邮电出版社,99.00
76,Airflow数据管道与工作流实战,Bas P. Harenslak,2020,电子工业出版社,109.00
77,Alluxio分布式存储系统实战,李浩源,2021,机械工业出版社,99.00
78,Doris大数据分析实战,陈明雨,2022,电子工业出版社,119.00
79,Pulsar消息队列实战,Jowanza Joseph,2021,人民邮电出版社,99.00
80,Superset数据可视化平台实战,Max Beauchemin,2020,机械工业出版社,89.00
81,TensorFlow深度学习实战,黄文坚,2018,人民邮电出版社,99.00
82,PyTorch深度学习实战,Eli Stevens,2020,人民邮电出版社,109.00
83,Keras深度学习实战,Antonio Gulli,2019,电子工业出版社,89.00
84,Caffe深度学习实战,Shawn Wildermuth,2018,机械工业出版社,99.00
85,MXNet深度学习实战,Thomas Delteil,2019,人民邮电出版社,89.00
86,Scikit-Learn机器学习实战,Aurélien Géron,2020,人民邮电出版社,99.00
87,XGBoost算法详解与实战,陈天奇,2021,电子工业出版社,89.00
88,LightGBM算法详解与实战,Guolin Ke,2020,机械工业出版社,79.00
89,CatBoost算法详解与实战,Anna Veronika Dorogush,2021,人民邮电出版社,89.00
90,BERT自然语言处理实战,黄泽杨,2020,电子工业出版社,99.00
91,GPT模型原理与实战,Jay Alammar,2022,人民邮电出版社,129.00
92,Transformer架构详解与实战,Alexander Rush,2021,机械工业出版社,109.00
93,Word2Vec词向量技术详解,Tomas Mikolov,2018,电子工业出版社,79.00
94,GloVe词向量技术详解,Jeffrey Pennington,2019,人民邮电出版社,69.00
95,FastText文本分类与词向量,Armand Joulin,2020,机械工业出版社,79.00
96,ELMo深度语境化词表示,Matthew E. Peters,2019,电子工业出版社,89.00
97,ResNet深度残差网络详解,Kaiming He,2018,人民邮电出版社,99.00
98,YOLO目标检测算法详解,Joseph Redmon,2020,机械工业出版社,109.00
99,Mask R-CNN实例分割算法详解,Kaiming He,2019,电子工业出版社,99.00
100,U-Net图像分割算法详解,Olaf Ronneberger,2018,人民邮电出版社,89.00
101,GAN生成对抗网络详解,Ian Goodfellow,2020,机械工业出版社,109.00
102,VAE变分自编码器详解,Diederik P. Kingma,2019,电子工业出版社,99.00
103,LSTM长短期记忆网络详解,Sepp Hochreiter,2018,人民邮电出版社,89.00
104,GRU门控循环单元详解,Kyunghyun Cho,2019,机械工业出版社,79.00
105,Attention注意力机制详解,Dzmitry Bahdanau,2020,电子工业出版社,99.00
