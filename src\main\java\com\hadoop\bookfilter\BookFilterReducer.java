package com.hadoop.bookfilter;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import java.io.IOException;

/**
 * 图书筛选Reducer类
 * 输入: (Text, Text) - 图书ID和完整图书信息
 * 输出: (Text, Text) - 图书ID和完整图书信息
 */
public class BookFilterReducer extends Reducer<Text, Text, Text, Text> {

    @Override
    protected void reduce(Text key, Iterable<Text> values, Context context) throws IOException, InterruptedException {
        // 对于每个图书ID，只输出一次图书信息
        // 由于我们的数据中每个图书ID应该是唯一的，所以这里只取第一个值
        for (Text value : values) {
            context.write(key, value);
            break; // 只处理第一个值
        }
    }
}
