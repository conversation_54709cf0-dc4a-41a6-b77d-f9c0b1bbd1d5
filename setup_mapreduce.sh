#!/bin/bash

# 这个脚本用于在Hadoop集群上设置MapReduce项目
# 作者：Augment Agent
# 日期：2025-05-19

# 显示脚本使用方法
show_usage() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -z, --zipfile PATH      指定7z压缩包路径 (默认: /home/<USER>"
    echo "  -d, --data PATH         指定数据文件路径 (默认: /home/<USER>/books.csv)"
    echo "  -o, --output DIR        指定解压目录 (默认: 压缩包所在目录)"
    echo "  -c, --clean             清理已存在的解压目录"
}

# 默认参数
ZIPFILE="/home/<USER>"
DATAFILE="/home/<USER>/books.csv"
OUTPUT_DIR=""
CLEAN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -h|--help)
            show_usage
            exit 0
            ;;
        -z|--zipfile)
            ZIPFILE="$2"
            shift
            shift
            ;;
        -d|--data)
            DATAFILE="$2"
            shift
            shift
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift
            shift
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        *)
            echo "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
done

# 如果没有指定输出目录，使用压缩包所在目录
if [ -z "$OUTPUT_DIR" ]; then
    OUTPUT_DIR=$(dirname "$ZIPFILE")
fi

# 显示当前配置
echo "当前配置:"
echo "  压缩包路径: $ZIPFILE"
echo "  数据文件路径: $DATAFILE"
echo "  解压目录: $OUTPUT_DIR"
echo "  清理模式: $CLEAN"
echo ""

# 检查压缩包是否存在
if [ ! -f "$ZIPFILE" ]; then
    echo "错误: 找不到压缩包文件: $ZIPFILE"
    exit 1
fi

# 检查数据文件是否存在
if [ ! -f "$DATAFILE" ]; then
    echo "警告: 找不到数据文件: $DATAFILE"
    echo "将继续执行，但可能需要手动准备数据文件"
fi

# 提取压缩包名称（不含路径和扩展名）
ZIPNAME=$(basename "$ZIPFILE" .7z)
PROJECT_DIR="$OUTPUT_DIR/$ZIPNAME"

# 如果需要清理，删除已存在的解压目录
if [ "$CLEAN" = true ] && [ -d "$PROJECT_DIR" ]; then
    echo "清理已存在的解压目录: $PROJECT_DIR"
    rm -rf "$PROJECT_DIR"
fi

# 创建解压目录
mkdir -p "$PROJECT_DIR"

# 解压7z文件
echo "解压压缩包: $ZIPFILE 到 $PROJECT_DIR"
echo "注意: 在Hadoop集群上，可能需要使用其他命令代替7z"

# 尝试使用7z命令
if command -v 7z &> /dev/null; then
    echo "使用7z命令解压..."
    cd "$OUTPUT_DIR"
    7z x "$ZIPFILE" -o"$PROJECT_DIR"
# 尝试使用p7zip命令
elif command -v p7zip &> /dev/null; then
    echo "使用p7zip命令解压..."
    cd "$OUTPUT_DIR"
    p7zip -d "$ZIPFILE" -o"$PROJECT_DIR"
# 尝试使用unzip命令（如果压缩包实际上是zip格式）
elif command -v unzip &> /dev/null; then
    echo "使用unzip命令解压..."
    cd "$OUTPUT_DIR"
    unzip "$ZIPFILE" -d "$PROJECT_DIR"
# 尝试使用tar命令（如果压缩包实际上是tar格式）
elif command -v tar &> /dev/null; then
    echo "使用tar命令解压..."
    cd "$OUTPUT_DIR"
    tar -xf "$ZIPFILE" -C "$PROJECT_DIR"
else
    echo "错误: 找不到可用的解压命令 (7z, p7zip, unzip, tar)"
    echo "请手动解压压缩包: $ZIPFILE"
    exit 1
fi

# 检查解压是否成功
if [ $? -ne 0 ]; then
    echo "解压失败，请手动解压压缩包: $ZIPFILE"
    exit 1
fi

echo "解压完成"

# 准备数据文件
echo "准备数据文件..."
echo "将数据文件上传到HDFS..."
hadoop fs -mkdir -p /user/hadoop/input
hadoop fs -put -f "$DATAFILE" /user/hadoop/input/

# 检查是否成功上传数据文件
if [ $? -ne 0 ]; then
    echo "警告: 上传数据文件到HDFS失败，请手动上传"
else
    echo "数据文件已成功上传到HDFS"
fi

# 编译项目
echo "编译项目..."
cd "$PROJECT_DIR"

# 检查是否有pom.xml文件
if [ -f "pom.xml" ]; then
    echo "检测到Maven项目，使用Maven编译..."
    if command -v mvn &> /dev/null; then
        mvn clean package
    else
        echo "警告: 找不到Maven命令，请手动编译项目"
    fi
else
    echo "使用传统方式编译..."
    mkdir -p target/classes
    javac -d target/classes -cp $(hadoop classpath) src/main/java/com/hadoop/bookfilter/*.java
    jar -cvf BookFilter.jar -C target/classes .
fi

# 检查是否成功编译
if [ -f "target/bookfilter-1.0-SNAPSHOT-jar-with-dependencies.jar" ]; then
    echo "Maven编译成功，生成的JAR文件: target/bookfilter-1.0-SNAPSHOT-jar-with-dependencies.jar"
    echo "可以使用以下命令运行MapReduce作业:"
    echo "hadoop jar target/bookfilter-1.0-SNAPSHOT-jar-with-dependencies.jar com.hadoop.bookfilter.BookFilterDriver /user/hadoop/input/books.csv /user/hadoop/output 2020"
elif [ -f "BookFilter.jar" ]; then
    echo "传统编译成功，生成的JAR文件: BookFilter.jar"
    echo "可以使用以下命令运行MapReduce作业:"
    echo "./run_mapreduce.sh"
else
    echo "警告: 编译可能失败，请手动编译项目"
fi

echo ""
echo "设置完成！"
echo "项目目录: $PROJECT_DIR"
echo "请进入项目目录并运行run_mapreduce.sh脚本:"
echo "cd $PROJECT_DIR"
echo "chmod +x run_mapreduce.sh"
echo "./run_mapreduce.sh"
