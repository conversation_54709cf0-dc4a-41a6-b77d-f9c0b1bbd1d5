# 图书筛选MapReduce项目

本项目使用MapReduce框架实现了筛选指定年份出版的图书功能。

## 环境要求

- Java 23
- Hadoop 3.1.3
- Hive
- IDEA开发环境

## 项目结构

```
MapReduce01/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── hadoop/
│   │   │           └── bookfilter/
│   │   │               ├── BookFilterLocalTest.java  # 本地测试类
│   │   │               ├── BookFilterDriver.java     # 集群提交类
│   │   │               ├── BookFilterMapper.java     # Mapper类
│   │   │               ├── BookFilterReducer.java    # Reducer类
│   │   │               ├── BookFilterCombiner.java   # Combiner类
│   │   │               ├── BookFilterPartitioner.java # Partitioner类
│   │   │               └── HiveIntegration.java      # Hive集成类
│   │   └── resources/
│   │       └── books.csv                # 示例数据
└── README.md
```

## 功能说明

本项目实现了以下功能：

1. 使用MapReduce框架筛选指定年份出版的图书
2. 使用Combiner和Partitioner策略优化MapReduce作业
3. 支持本地测试和集群提交
4. 与Hive数据仓库集成

## 使用方法

### 本地测试

1. 在IDEA中运行`BookFilterLocalTest`类
2. 默认筛选2020年出版的图书
3. 结果将保存在`output`目录中

### 集群提交

#### 使用Maven构建（推荐）

1. 使用Maven编译和打包项目：

```bash
mvn clean package
```

2. 提交到Hadoop集群：

```bash
# 使用包含所有依赖的JAR
hadoop jar target/bookfilter-1.0-SNAPSHOT-jar-with-dependencies.jar com.hadoop.bookfilter.BookFilterDriver <输入路径> <输出路径> <目标年份>

# 或使用不含依赖的JAR（依赖由Hadoop环境提供）
hadoop jar target/bookfilter-1.0-SNAPSHOT.jar com.hadoop.bookfilter.BookFilterDriver <输入路径> <输出路径> <目标年份>
```

例如：

```bash
hadoop jar target/bookfilter-1.0-SNAPSHOT-jar-with-dependencies.jar com.hadoop.bookfilter.BookFilterDriver /user/hadoop/input/books.csv /user/hadoop/output 2020
```

#### 传统方式构建

如果集群上没有安装Maven，也可以使用传统方式：

1. 编译项目：

```bash
mkdir -p target/classes
javac -d target/classes -cp $(hadoop classpath) src/main/java/com/hadoop/bookfilter/*.java
```

2. 创建JAR文件：

```bash
jar -cvf BookFilter.jar -C target/classes .
```

3. 提交到Hadoop集群：

```bash
hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver <输入路径> <输出路径> <目标年份>
```

例如：

```bash
hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver /user/hadoop/input/books.csv /user/hadoop/output 2020
```

### Hive集成

#### 使用Maven构建的JAR

1. 使用Maven打包项目（如上所述）
2. 使用以下命令与Hive集成：

```bash
# 使用包含所有依赖的JAR
java -cp target/bookfilter-1.0-SNAPSHOT-jar-with-dependencies.jar com.hadoop.bookfilter.HiveIntegration <数据文件路径> <目标年份>

# 或使用不含依赖的JAR，但需要包含Hive依赖
java -cp target/bookfilter-1.0-SNAPSHOT.jar:$HIVE_HOME/lib/* com.hadoop.bookfilter.HiveIntegration <数据文件路径> <目标年份>
```

例如：

```bash
java -cp target/bookfilter-1.0-SNAPSHOT-jar-with-dependencies.jar com.hadoop.bookfilter.HiveIntegration /user/hadoop/input/books.csv 2020
```

#### 使用传统方式构建的JAR

如果使用传统方式构建JAR文件：

```bash
java -cp BookFilter.jar:$HIVE_HOME/lib/* com.hadoop.bookfilter.HiveIntegration <数据文件路径> <目标年份>
```

例如：

```bash
java -cp BookFilter.jar:$HIVE_HOME/lib/* com.hadoop.bookfilter.HiveIntegration /user/hadoop/input/books.csv 2020
```

## 优化策略

1. **Combiner优化**：在Map阶段后进行本地合并，减少网络传输
2. **Partitioner优化**：根据图书ID的哈希值进行分区，确保相同的图书ID总是被发送到相同的Reducer

## Java 23兼容性

本项目已适配Java 23，通过以下方式解决兼容性问题：

1. 设置系统属性`java.security.manager`为`allow`
2. 设置MapReduce作业类加载器配置
3. 禁用Java 23中的一些警告
4. 提供不依赖Hadoop MapReduce框架的简化实现（SimpleBookFilter类）

如果在集群上运行时遇到Java 23兼容性问题，项目会自动回退到使用SimpleBookFilter类，确保作业能够完成。

### Maven配置

项目的Maven配置（pom.xml）已经设置了Java 23兼容性：

```xml
<properties>
    <maven.compiler.source>23</maven.compiler.source>
    <maven.compiler.target>23</maven.compiler.target>
</properties>
```

并且配置了编译器参数：

```xml
<compilerArgs>
    <arg>--enable-preview</arg>
</compilerArgs>
```

## 集群运行说明

本项目已经上传到Hadoop集群，相关路径如下：

- **压缩包路径**：`/home/<USER>
- **数据文件路径**：`/home/<USER>/books.csv`

### 1. 使用自动化脚本（推荐）

项目提供了两个脚本来简化部署和运行过程：
- `setup_mapreduce.sh`：用于解压7z文件、准备数据和编译项目
- `run_mapreduce.sh`：用于运行MapReduce作业，提供多种处理依赖的方法

#### 步骤一：设置项目

登录到Hadoop集群后，按照以下步骤操作：

```bash
# 1. 下载setup_mapreduce.sh脚本（如果尚未下载）
wget https://raw.githubusercontent.com/yourusername/MapReduce01/main/setup_mapreduce.sh
# 或者手动创建此脚本

# 2. 给脚本添加执行权限
chmod +x setup_mapreduce.sh

# 3. 运行设置脚本
./setup_mapreduce.sh

# 4. 如果需要自定义路径，可以使用选项
./setup_mapreduce.sh --zipfile /path/to/MapReduce01.7z --data /path/to/books.csv
```

#### 步骤二：运行MapReduce作业

设置完成后，使用run_mapreduce.sh脚本运行作业：

```bash
# 1. 进入项目目录
cd /home/<USER>

# 2. 给脚本添加执行权限
chmod +x run_mapreduce.sh

# 3. 运行MapReduce作业
./run_mapreduce.sh

# 4. 如果需要自定义参数，可以使用选项
./run_mapreduce.sh -y 2015 -m 4
```

### 2. 手动设置和运行

如果您不想使用脚本，也可以手动执行以下步骤：

```bash
# 1. 解压压缩包
cd /home
# 如果有7z命令
7z x MapReduce01.7z
# 如果没有7z命令，尝试其他解压命令
# unzip MapReduce01.zip 或 tar -xf MapReduce01.tar

# 2. 确保数据文件可用
ls -l /home/<USER>/books.csv

# 3. 将数据文件上传到HDFS
hadoop fs -mkdir -p /user/hadoop/input
hadoop fs -put /home/<USER>/books.csv /user/hadoop/input/

# 4. 编译项目
cd /home/<USER>
mkdir -p target/classes
javac -d target/classes -cp $(hadoop classpath) src/main/java/com/hadoop/bookfilter/*.java
jar -cvf BookFilter.jar -C target/classes .

# 5. 删除已存在的输出目录
hadoop fs -rm -r /user/hadoop/output

# 6. 提交MapReduce作业
hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver /user/hadoop/input/books.csv /user/hadoop/output 2020
```

### 3. 查看结果

作业完成后，可以使用以下命令查看结果：

```bash
# 查看输出内容
hadoop fs -cat /user/hadoop/output/part-r-*
```

## 项目依赖

本项目使用Maven管理依赖，主要依赖如下：

### Hadoop依赖

```xml
<!-- Hadoop Core Dependencies -->
<dependency>
    <groupId>org.apache.hadoop</groupId>
    <artifactId>hadoop-common</artifactId>
    <version>${hadoop.version}</version>
    <scope>provided</scope>
</dependency>
<dependency>
    <groupId>org.apache.hadoop</groupId>
    <artifactId>hadoop-mapreduce-client-core</artifactId>
    <version>${hadoop.version}</version>
    <scope>provided</scope>
</dependency>
<dependency>
    <groupId>org.apache.hadoop</groupId>
    <artifactId>hadoop-mapreduce-client-common</artifactId>
    <version>${hadoop.version}</version>
    <scope>provided</scope>
</dependency>
<dependency>
    <groupId>org.apache.hadoop</groupId>
    <artifactId>hadoop-hdfs</artifactId>
    <version>${hadoop.version}</version>
    <scope>provided</scope>
</dependency>
<dependency>
    <groupId>org.apache.hadoop</groupId>
    <artifactId>hadoop-client</artifactId>
    <version>${hadoop.version}</version>
    <scope>provided</scope>
</dependency>
```

### Hive依赖

```xml
<!-- Hive Dependencies (for HiveIntegration) -->
<dependency>
    <groupId>org.apache.hive</groupId>
    <artifactId>hive-jdbc</artifactId>
    <version>${hive.version}</version>
    <scope>provided</scope>
</dependency>
```

所有依赖都使用`provided`作用域，这意味着它们在编译时需要，但在运行时由Hadoop环境提供。这样可以减小JAR文件的大小，并避免版本冲突。

如果需要包含所有依赖，可以使用Maven的assembly或shade插件创建"胖JAR"文件。

## 脚本运行指南

如果您遇到依赖问题或者希望简化运行过程，可以使用以下脚本方法：

### 1. 使用run_mapreduce.sh脚本（推荐）

项目中包含了一个`run_mapreduce.sh`脚本，它提供了多种处理依赖的方法：

```bash
# 进入项目目录
cd /home/<USER>

# 给脚本添加执行权限
chmod +x run_mapreduce.sh

# 查看脚本帮助信息
./run_mapreduce.sh --help
```

脚本支持以下选项：
- `-h, --help`：显示帮助信息
- `-c, --clean`：清理临时文件和输出目录
- `-y, --year YEAR`：指定筛选的年份（默认：2020）
- `-m, --method NUM`：指定使用的方法（1-5，默认：1）
  - 1: 设置HADOOP_CLASSPATH
  - 2: 使用-libjars选项
  - 3: 使用-files选项
  - 4: 创建胖JAR文件
  - 5: 直接使用hadoop命令
- `-i, --input PATH`：指定输入路径（默认：/user/hadoop/input/books.csv）
- `-o, --output PATH`：指定输出路径（默认：/user/hadoop/output）

#### 示例用法：

```bash
# 使用默认参数运行（方法1，2020年，默认输入输出路径）
./run_mapreduce.sh

# 指定年份为2015
./run_mapreduce.sh -y 2015

# 使用方法4（创建胖JAR）并指定输入输出路径
./run_mapreduce.sh -m 4 -i /user/hadoop/mydata/books.csv -o /user/hadoop/myresults

# 清理临时文件并使用方法2运行
./run_mapreduce.sh -c -m 2
```

### 2. 手动运行命令

如果您不想使用脚本，也可以直接运行以下命令：

#### 方法1：设置HADOOP_CLASSPATH

```bash
export HADOOP_CLASSPATH=$(hadoop classpath)
hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver /user/hadoop/input/books.csv /user/hadoop/output 2020
```

#### 方法2：使用-libjars选项

```bash
hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver -libjars $HADOOP_HOME/share/hadoop/mapreduce/hadoop-mapreduce-client-core-*.jar,$HADOOP_HOME/share/hadoop/mapreduce/hadoop-mapreduce-client-common-*.jar /user/hadoop/input/books.csv /user/hadoop/output 2020
```

#### 方法3：使用-files选项

```bash
hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver -files BookFilter.jar /user/hadoop/input/books.csv /user/hadoop/output 2020
```

#### 方法4：创建胖JAR文件

```bash
# 创建临时目录
mkdir -p fatjar_temp

# 解压JAR文件
cd fatjar_temp
jar xf ../BookFilter.jar

# 复制Hadoop依赖
cp -r $HADOOP_HOME/share/hadoop/common/lib/* .
cp -r $HADOOP_HOME/share/hadoop/mapreduce/lib/* .
cp -r $HADOOP_HOME/share/hadoop/mapreduce/*.jar .

# 创建胖JAR文件
jar cvf ../BookFilterFat.jar *
cd ..

# 运行MapReduce作业
hadoop jar BookFilterFat.jar com.hadoop.bookfilter.BookFilterDriver /user/hadoop/input/books.csv /user/hadoop/output 2020
```

#### 方法5：直接使用hadoop命令

```bash
hadoop com.hadoop.bookfilter.BookFilterDriver /user/hadoop/input/books.csv /user/hadoop/output 2020
```