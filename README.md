# 图书筛选MapReduce项目

本项目使用MapReduce框架实现了筛选指定年份出版的图书功能。

## 环境要求

- Java 23
- Hadoop 3.1.3
- Hive
- Linux虚拟机环境

## 项目结构

```
MapReduce01/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── hadoop/
│   │   │           └── bookfilter/
│   │   │               ├── BookFilterLocalTest.java  # 本地测试类
│   │   │               ├── BookFilterDriver.java     # 集群提交类
│   │   │               ├── BookFilterMapper.java     # Mapper类
│   │   │               ├── BookFilterReducer.java    # Reducer类
│   │   │               ├── BookFilterCombiner.java   # Combiner类
│   │   │               ├── BookFilterPartitioner.java # Partitioner类
│   │   │               └── HiveIntegration.java      # Hive集成类
│   │   └── resources/
│   │       └── books.csv                # 示例数据
└── README.md
```

## 功能说明

本项目实现了以下功能：

1. 使用MapReduce框架筛选指定年份出版的图书
2. 使用Combiner和Partitioner策略优化MapReduce作业
3. 支持本地测试和集群提交
4. 与Hive数据仓库集成

## 使用方法

### 虚拟机部署和运行指南

本指南适用于直接将项目文件拖拽到虚拟机中的情况，不使用Maven构建。

#### 步骤1：准备项目文件
**在虚拟机的任意目录下（建议在用户主目录）**

```bash
# 假设您已经将项目文件拖拽到虚拟机的 /home/<USER>/ 目录下
# 进入用户主目录
cd ~

# 查看项目文件是否存在
ls -la MapReduce01/
```

#### 步骤2：准备数据文件
**在项目根目录下**

```bash
# 进入项目目录
cd ~/MapReduce01

# 检查数据文件是否存在
ls -la src/main/resources/books.csv

# 如果数据文件不存在，请确保books.csv文件在正确位置
# 数据文件应该包含3000条图书记录
```

#### 步骤3：创建必要的目录结构
**在项目根目录下**

```bash
# 当前目录：~/MapReduce01
mkdir -p target/classes
mkdir -p input
mkdir -p output

# 复制数据文件到input目录（用于本地测试）
cp src/main/resources/books.csv input/
```

#### 步骤4：编译Java源代码
**在项目根目录下**

```bash
# 当前目录：~/MapReduce01
# 编译所有Java文件
javac -d target/classes -cp $(hadoop classpath) src/main/java/com/hadoop/bookfilter/*.java

# 检查编译是否成功
ls -la target/classes/com/hadoop/bookfilter/
```

#### 步骤5：创建JAR文件
**在项目根目录下**

```bash
# 当前目录：~/MapReduce01
# 创建JAR文件
jar -cvf BookFilter.jar -C target/classes .

# 检查JAR文件是否创建成功
ls -la BookFilter.jar
```

#### 步骤6：准备HDFS数据
**在项目根目录下**

```bash
# 当前目录：~/MapReduce01
# 启动Hadoop服务（如果尚未启动）
start-dfs.sh
start-yarn.sh

# 创建HDFS目录
hadoop fs -mkdir -p /user/$(whoami)/input
hadoop fs -mkdir -p /user/$(whoami)/output

# 上传数据文件到HDFS
hadoop fs -put src/main/resources/books.csv /user/$(whoami)/input/

# 验证文件上传成功
hadoop fs -ls /user/$(whoami)/input/
```

#### 步骤7：运行MapReduce作业
**在项目根目录下**

```bash
# 当前目录：~/MapReduce01
# 删除之前的输出目录（如果存在）
hadoop fs -rm -r /user/$(whoami)/output

# 提交MapReduce作业（筛选2020年出版的图书）
hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver /user/$(whoami)/input/books.csv /user/$(whoami)/output 2020
```

#### 步骤8：查看运行结果
**在项目根目录下**

```bash
# 当前目录：~/MapReduce01
# 查看作业运行状态
hadoop job -list

# 查看输出结果
hadoop fs -cat /user/$(whoami)/output/part-r-*

# 将结果下载到本地查看
hadoop fs -get /user/$(whoami)/output/part-r-* ./result.txt
cat result.txt
```

### 本地测试（可选）

如果您想在提交到集群之前进行本地测试：

#### 本地测试步骤
**在项目根目录下**

```bash
# 当前目录：~/MapReduce01
# 运行本地测试
java -cp target/classes:$(hadoop classpath) com.hadoop.bookfilter.BookFilterLocalTest

# 查看本地测试结果
ls -la output/
cat output/part-r-00000
```

### Hive集成

#### 使用Hive进行数据分析
**在项目根目录下**

```bash
# 当前目录：~/MapReduce01
# 运行Hive集成程序
java -cp BookFilter.jar:$HIVE_HOME/lib/* com.hadoop.bookfilter.HiveIntegration /user/$(whoami)/input/books.csv 2020
```

### 常见问题排查

#### 问题1：编译失败
**解决方案：**
```bash
# 当前目录：~/MapReduce01
# 检查Java版本
java -version

# 检查Hadoop环境变量
echo $HADOOP_HOME
echo $HADOOP_CONF_DIR

# 重新设置环境变量（如果需要）
export HADOOP_HOME=/opt/hadoop
export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop
export PATH=$PATH:$HADOOP_HOME/bin:$HADOOP_HOME/sbin
```

#### 问题2：HDFS权限问题
**解决方案：**
```bash
# 当前目录：~/MapReduce01
# 检查HDFS权限
hadoop fs -ls /user/

# 创建用户目录（如果不存在）
hadoop fs -mkdir -p /user/$(whoami)
hadoop fs -chown $(whoami):$(whoami) /user/$(whoami)
```

#### 问题3：作业运行失败
**解决方案：**
```bash
# 当前目录：~/MapReduce01
# 查看作业日志
yarn logs -applicationId <application_id>

# 检查YARN服务状态
yarn node -list
```

## 优化策略

1. **Combiner优化**：在Map阶段后进行本地合并，减少网络传输
2. **Partitioner优化**：根据图书ID的哈希值进行分区，确保相同的图书ID总是被发送到相同的Reducer

## Java 23兼容性

本项目已适配Java 23，通过以下方式解决兼容性问题：

1. 设置系统属性`java.security.manager`为`allow`
2. 设置MapReduce作业类加载器配置
3. 禁用Java 23中的一些警告
4. 提供不依赖Hadoop MapReduce框架的简化实现（SimpleBookFilter类）

如果在集群上运行时遇到Java 23兼容性问题，项目会自动回退到使用SimpleBookFilter类，确保作业能够完成。

### Maven配置

项目的Maven配置（pom.xml）已经设置了Java 23兼容性：

```xml
<properties>
    <maven.compiler.source>23</maven.compiler.source>
    <maven.compiler.target>23</maven.compiler.target>
</properties>
```

并且配置了编译器参数：

```xml
<compilerArgs>
    <arg>--enable-preview</arg>
</compilerArgs>
```

## 虚拟机环境配置

### 环境变量设置
**在任意目录下执行**

```bash
# 检查当前环境变量
echo $HADOOP_HOME
echo $JAVA_HOME

# 如果环境变量未设置，请添加到 ~/.bashrc 文件中
echo 'export JAVA_HOME=/usr/lib/jvm/java-23-openjdk' >> ~/.bashrc
echo 'export HADOOP_HOME=/opt/hadoop' >> ~/.bashrc
echo 'export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop' >> ~/.bashrc
echo 'export PATH=$PATH:$HADOOP_HOME/bin:$HADOOP_HOME/sbin' >> ~/.bashrc

# 重新加载环境变量
source ~/.bashrc
```

### 数据集说明

本项目使用的数据集包含3000条图书记录，格式如下：
```
ID,书名,作者,年份,出版社,价格
1,Java编程思想,Bruce Eckel,2007,机械工业出版社,108.00
2,Hadoop权威指南,Tom White,2015,清华大学出版社,119.00
...
```

### 不同年份的筛选示例

您可以筛选不同年份的图书：

```bash
# 当前目录：~/MapReduce01
# 筛选2015年出版的图书
hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver /user/$(whoami)/input/books.csv /user/$(whoami)/output_2015 2015

# 筛选2018年出版的图书
hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver /user/$(whoami)/input/books.csv /user/$(whoami)/output_2018 2018

# 筛选2022年出版的图书
hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver /user/$(whoami)/input/books.csv /user/$(whoami)/output_2022 2022
```

## 项目依赖

本项目依赖以下组件，这些组件应该已经在您的Hadoop环境中安装：

- **Hadoop 3.1.3**：提供MapReduce框架和HDFS存储
- **Java 23**：运行环境
- **Hive**（可选）：用于数据仓库集成

### 依赖检查
**在任意目录下执行**

```bash
# 检查Hadoop版本
hadoop version

# 检查Java版本
java -version

# 检查Hive版本（如果安装了Hive）
hive --version

# 检查HDFS状态
hdfs dfsadmin -report

# 检查YARN状态
yarn node -list
```

## 性能监控

### 查看作业执行情况
**在项目根目录下**

```bash
# 当前目录：~/MapReduce01
# 查看正在运行的作业
yarn application -list

# 查看作业详细信息
yarn application -status <application_id>

# 查看作业日志
yarn logs -applicationId <application_id>

# 通过Web界面查看
# ResourceManager: http://localhost:8088
# NameNode: http://localhost:9870
```

### 性能优化建议

1. **调整Map和Reduce任务数量**：
```bash
# 当前目录：~/MapReduce01
# 设置Map任务数量
hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver -D mapreduce.job.maps=4 /user/$(whoami)/input/books.csv /user/$(whoami)/output 2020

# 设置Reduce任务数量
hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver -D mapreduce.job.reduces=2 /user/$(whoami)/input/books.csv /user/$(whoami)/output 2020
```

2. **调整内存设置**：
```bash
# 当前目录：~/MapReduce01
hadoop jar BookFilter.jar com.hadoop.bookfilter.BookFilterDriver -D mapreduce.map.memory.mb=1024 -D mapreduce.reduce.memory.mb=2048 /user/$(whoami)/input/books.csv /user/$(whoami)/output 2020
```