package com.hadoop.bookfilter;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;

import java.io.File;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 本地测试类 - 适配Java 23
 */
public class BookFilterLocalTest {

    static {
        // 为Java 23设置系统属性，允许安全管理器
        System.setProperty("java.security.manager", "allow");
    }

    public static void main(String[] args) throws Exception {
        // 检查是否为Java 23或更高版本
        String javaVersion = System.getProperty("java.version");
        System.out.println("当前Java版本: " + javaVersion);

        if (javaVersion.startsWith("23") || javaVersion.startsWith("24")) {
            System.out.println("检测到Java 23+，使用简化版本的MapReduce处理...");
            processWithSimpleImplementation();
            return;
        }

        // 设置为本地模式
        Configuration conf = new Configuration();
        conf.set("mapreduce.framework.name", "local");
        conf.set("fs.defaultFS", "file:///");

        // 设置目标年份
        int targetYear = 2020;
        conf.setInt("target.year", targetYear);

        // 输入输出路径
        String inputPath = "src/main/resources/books.csv";
        String outputPath = "output";

        // 删除已存在的输出目录
        File outputDir = new File(outputPath);
        if (outputDir.exists()) {
            deleteDir(outputDir);
        }

        // 创建作业
        Job job = Job.getInstance(conf, "Book Filter Local Test");
        job.setJarByClass(BookFilterLocalTest.class);

        // 设置Mapper和Reducer类
        job.setMapperClass(BookFilterMapper.class);
        job.setReducerClass(BookFilterReducer.class);

        // 设置Combiner和Partitioner（优化）
        job.setCombinerClass(BookFilterCombiner.class);
        job.setPartitionerClass(BookFilterPartitioner.class);

        // 设置输出键值类型
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);

        // 设置Reducer数量
        job.setNumReduceTasks(1);

        // 设置输入输出路径
        FileInputFormat.addInputPath(job, new Path(inputPath));
        FileOutputFormat.setOutputPath(job, new Path(outputPath));

        // 提交作业并等待完成
        boolean success = job.waitForCompletion(true);

        System.out.println("Job " + (success ? "completed successfully" : "failed"));
        System.exit(success ? 0 : 1);
    }

    /**
     * 递归删除目录
     */
    private static boolean deleteDir(File dir) {
        if (dir.isDirectory()) {
            String[] children = dir.list();
            if (children != null) {
                for (String child : children) {
                    boolean success = deleteDir(new File(dir, child));
                    if (!success) {
                        return false;
                    }
                }
            }
        }
        return dir.delete();
    }

    /**
     * 使用简单的Java实现来处理图书筛选，不依赖Hadoop MapReduce框架
     * 这是为了解决Java 23兼容性问题
     */
    private static void processWithSimpleImplementation() throws IOException {
        System.out.println("开始使用简化版本处理图书筛选...");

        // 设置目标年份
        int targetYear = 2020;
        System.out.println("目标年份: " + targetYear);

        // 输入输出路径
        String inputPath = "src/main/resources/books.csv";
        String outputPath = "output";

        // 创建输出目录
        File outputDir = new File(outputPath);
        if (outputDir.exists()) {
            deleteDir(outputDir);
        }
        outputDir.mkdirs();

        // 读取输入文件
        List<String> filteredBooks = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(inputPath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                String[] fields = line.split(",");

                // 确保数据格式正确
                if (fields.length >= 4) {
                    try {
                        // 获取出版年份
                        int publishYear = Integer.parseInt(fields[3]);

                        // 如果年份匹配，则添加到结果列表
                        if (publishYear == targetYear) {
                            filteredBooks.add(line);
                        }
                    } catch (NumberFormatException e) {
                        // 忽略无法解析年份的记录
                        System.err.println("无法解析年份: " + line);
                    }
                }
            }
        }

        // 写入输出文件
        File outputFile = new File(outputPath, "part-r-00000");
        try (FileWriter writer = new FileWriter(outputFile)) {
            for (String book : filteredBooks) {
                String[] fields = book.split(",");
                writer.write(fields[0] + "\t" + book + "\n");
            }
        }

        // 打印结果
        System.out.println("筛选完成，共找到 " + filteredBooks.size() + " 本 " + targetYear + " 年出版的图书");
        System.out.println("结果已保存到 " + outputFile.getAbsolutePath());

        // 显示筛选结果
        System.out.println("\n筛选结果：");
        System.out.println("图书ID\t书名\t作者\t出版年份\t出版社\t价格");
        for (String book : filteredBooks) {
            String[] fields = book.split(",");
            System.out.println(fields[0] + "\t" + fields[1] + "\t" + fields[2] + "\t" +
                    fields[3] + "\t" + fields[4] + "\t" + fields[5]);
        }

        System.out.println("\nJob completed successfully");
    }
}
