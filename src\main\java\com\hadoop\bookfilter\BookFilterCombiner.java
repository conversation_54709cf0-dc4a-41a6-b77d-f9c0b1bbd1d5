package com.hadoop.bookfilter;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import java.io.IOException;

/**
 * 图书筛选Combiner类
 * 用于在Map阶段后进行本地合并，减少网络传输
 * 输入: (Text, Text) - 图书ID和完整图书信息
 * 输出: (Text, Text) - 图书ID和完整图书信息
 */
public class BookFilterCombiner extends Reducer<Text, Text, Text, Text> {

    @Override
    protected void reduce(Text key, Iterable<Text> values, Context context) throws IOException, InterruptedException {
        // Combiner的逻辑与Reducer相同
        // 对于每个图书ID，只输出一次图书信息
        for (Text value : values) {
            context.write(key, value);
            break; // 只处理第一个值
        }
    }
}
