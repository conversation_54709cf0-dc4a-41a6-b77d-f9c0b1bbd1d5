package com.hadoop.bookfilter;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.util.GenericOptionsParser;

/**
 * 图书筛选Driver类
 * 用于提交MapReduce作业到Hadoop集群
 */
public class BookFilterDriver {

    static {
        // 为Java 23设置系统属性，允许安全管理器
        System.setProperty("java.security.manager", "allow");

        // 禁用警告
        disableWarnings();
    }

    /**
     * 禁用Java 23中的一些警告
     */
    private static void disableWarnings() {
        try {
            // 禁用Java 9+的非法反射警告
            System.setProperty("illegal-access", "permit");
            // 禁用Java 23的模块警告
            System.setProperty("jdk.module.illegalAccess.silent", "true");
        } catch (Exception e) {
            // 忽略异常
        }
    }

    public static void main(String[] args) throws Exception {
        // 检查是否为Java 23或更高版本
        String javaVersion = System.getProperty("java.version");
        System.out.println("当前Java版本: " + javaVersion);

        try {
            // 创建配置
            Configuration conf = new Configuration();

            // 解析命令行参数
            String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
            if (otherArgs.length < 3) {
                System.err.println("用法: BookFilterDriver <输入路径> <输出路径> <目标年份>");
                System.exit(2);
            }

            // 设置目标年份
            int targetYear = Integer.parseInt(otherArgs[2]);
            conf.setInt("target.year", targetYear);

            // 设置Java 23兼容性配置
            conf.set("mapreduce.job.classloader", "true");
            conf.set("mapreduce.job.classloader.system.classes", "-java.,java.lang.,javax.,org.apache.hadoop.");

            // 创建作业
            Job job = Job.getInstance(conf, "Book Filter");
            job.setJarByClass(BookFilterDriver.class);

            // 设置Mapper和Reducer类
            job.setMapperClass(BookFilterMapper.class);
            job.setReducerClass(BookFilterReducer.class);

            // 设置Combiner和Partitioner（优化）
            job.setCombinerClass(BookFilterCombiner.class);
            job.setPartitionerClass(BookFilterPartitioner.class);

            // 设置输出键值类型
            job.setOutputKeyClass(Text.class);
            job.setOutputValueClass(Text.class);

            // 设置Reducer数量
            job.setNumReduceTasks(2);

            // 设置输入输出路径
            FileInputFormat.addInputPath(job, new Path(otherArgs[0]));
            FileOutputFormat.setOutputPath(job, new Path(otherArgs[1]));

            // 提交作业并等待完成
            boolean success = job.waitForCompletion(true);

            System.out.println("Job " + (success ? "completed successfully" : "failed"));
            System.exit(success ? 0 : 1);
        } catch (Exception e) {
            System.err.println("执行MapReduce作业时发生错误: " + e.getMessage());
            e.printStackTrace();

            // 如果发生错误，尝试使用简化版本
            System.out.println("\n尝试使用简化版本处理...");
            SimpleBookFilter.main(args);
        }
    }
}
