package com.hadoop.bookfilter;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Partitioner;

/**
 * 图书筛选Partitioner类
 * 用于控制中间结果如何分配到Reducer
 * 这里根据图书ID的哈希值进行分区
 */
public class BookFilterPartitioner extends Partitioner<Text, Text> {

    @Override
    public int getPartition(Text key, Text value, int numReduceTasks) {
        // 如果只有一个Reducer，直接返回0
        if (numReduceTasks == 0) {
            return 0;
        }
        
        // 获取图书ID
        String bookId = key.toString();
        
        // 根据图书ID的哈希值分配到不同的Reducer
        // 确保相同的图书ID总是被发送到相同的Reducer
        return Math.abs(bookId.hashCode()) % numReduceTasks;
    }
}
