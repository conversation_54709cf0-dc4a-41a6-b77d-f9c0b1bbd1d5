package com.hadoop.bookfilter;

import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.hadoop.conf.Configuration;

import java.io.IOException;

/**
 * 图书筛选Mapper类
 * 输入: (LongWritable, Text) - 行号和行内容
 * 输出: (Text, Text) - 图书ID和完整图书信息
 */
public class BookFilterMapper extends Mapper<LongWritable, Text, Text, Text> {

    private int targetYear;

    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        // 从配置中获取目标年份
        Configuration conf = context.getConfiguration();
        targetYear = conf.getInt("target.year", 2020); // 默认为2020年
    }

    @Override
    protected void map(LongWritable key, Text value, Context context) throws IOException, InterruptedException {
        // 解析CSV行
        String line = value.toString();
        String[] fields = line.split(",");

        // 确保数据格式正确
        if (fields.length >= 4) {
            try {
                // 获取出版年份
                int publishYear = Integer.parseInt(fields[3]);

                // 如果年份匹配，则输出
                if (publishYear == targetYear) {
                    // 使用图书ID作为键
                    Text bookId = new Text(fields[0]);
                    // 使用完整图书信息作为值
                    Text bookInfo = new Text(line);
                    context.write(bookId, bookInfo);
                }
            } catch (NumberFormatException e) {
                // 忽略无法解析年份的记录
                System.err.println("无法解析年份: " + line);
            }
        }
    }
}
